import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Activation, Conv1D, Flatten
from keras.optimizers import <PERSON>
from haversine import haversine
from filterpy.kalman import KalmanFilter

# ---------------- 1. 读取数据 ----------------
data = pd.read_csv('../../Geolife Trajectories 1.3\\Data\\001\\Trajectory\\20081024234405.plt',
                   skiprows=6, header=None)
data.columns = ['lat', 'lon', 'zero', 'alt', 'date_days', 'date', 'time']
data = data[['lat', 'lon']]
print(f'样本数：{len(data)}, 维度：{data.shape[1]}')

# ---------------- 2. 数据归一化 ----------------
scaler = MinMaxScaler()
data_scaled = scaler.fit_transform(data)


# ---------------- 3. 构造序列数据 ----------------
def create_dataset(dataset, time_step=5):
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        Y.append(dataset[i + time_step, :])
    return np.array(X), np.array(Y)


time_step = 5
X, Y = create_dataset(data_scaled, time_step)

# ---------------- 4. 划分训练集 ----------------
train_size = int(len(X) * 0.8)
trainX, trainY = X[:train_size], Y[:train_size]
testX, testY = X[train_size:], Y[train_size:]
print('Train X shape:', trainX.shape, 'Train Y shape:', trainY.shape)
print('Test X shape:', testX.shape, 'Test Y shape:', testY.shape)

# ---------------- 5. LSTM 模型 ----------------
lstm_model = Sequential([
    LSTM(120, return_sequences=True, input_shape=(time_step, 2)),
    Dropout(0.2),
    LSTM(120),
    Dropout(0.2),
    Dense(2, activation='linear')
])
lstm_model.compile(loss='mse', optimizer=Adam(0.001), metrics=['mae'])
lstm_model.fit(trainX, trainY, epochs=100, batch_size=64, validation_split=0.2, verbose=0)

# ---------------- 6. CNN 模型 ----------------
cnn_model = Sequential([
    Conv1D(64, kernel_size=2, activation='relu', input_shape=(time_step, 2)),
    Conv1D(64, kernel_size=2, activation='relu'),
    Flatten(),
    Dense(50, activation='relu'),
    Dense(2, activation='linear')
])
cnn_model.compile(loss='mse', optimizer=Adam(0.001), metrics=['mae'])
cnn_model.fit(trainX, trainY, epochs=50, batch_size=64, validation_split=0.2, verbose=0)


# ---------------- 7. 卡尔曼滤波预测 ----------------
def kalman_predict(data_array):
    kf = KalmanFilter(dim_x=4, dim_z=2)
    dt = 1.0
    # 状态转移矩阵
    kf.F = np.array([[1, 0, dt, 0],
                     [0, 1, 0, dt],
                     [0, 0, 1, 0],
                     [0, 0, 0, 1]])
    kf.H = np.array([[1, 0, 0, 0],
                     [0, 1, 0, 0]])
    kf.P *= 10
    kf.R = np.eye(2) * 0.01
    kf.Q = np.eye(4) * 0.0001
    # 初始化状态向量
    kf.x = np.array([[data_array[0, 0]],
                     [data_array[0, 1]],
                     [0.0],
                     [0.0]])

    preds = []
    for point in data_array:
        kf.predict()
        kf.update(point)
        preds.append(kf.x[:2].flatten())  # 转为一维数组
    return np.array(preds)


# 将训练集最后一点与测试集拼接作为初始输入
testX_flat = np.concatenate((trainX[-1:], testX), axis=0).reshape(-1, 2)
kf_pred_scaled = kalman_predict(testX_flat)
kf_pred = scaler.inverse_transform(kf_pred_scaled[1:len(testY) + 1])  # 丢掉第一步

# ---------------- 8. 预测 & 反归一化 ----------------
lstm_pred = scaler.inverse_transform(lstm_model.predict(testX))
cnn_pred = scaler.inverse_transform(cnn_model.predict(testX))
testY_inverse = scaler.inverse_transform(testY)


# ---------------- 9. 计算误差 ----------------
def calc_haversine_error(true, pred):
    errors = []
    for t, p in zip(true, pred):
        errors.append(haversine((t[0], t[1]), (p[0], p[1])) * 1000)
    return np.mean(errors)


print("LSTM 平均误差: {:.2f} m".format(calc_haversine_error(testY_inverse, lstm_pred)))
print("CNN 平均误差: {:.2f} m".format(calc_haversine_error(testY_inverse, cnn_pred)))
print("Kalman 平均误差: {:.2f} m".format(calc_haversine_error(testY_inverse, kf_pred)))

# ---------------- 10. 可视化 ----------------
# ---------------- 绘制轨迹对比图 ----------------
pred_lstm_inverse = lstm_pred
pred_cnn_inverse = cnn_pred
pred_kf_inverse = kf_pred

plt.figure(figsize=(8,6))
plt.plot(testY_inverse[:,1], testY_inverse[:,0], label='True trajectory', marker='o', markersize=2, linestyle='-')
plt.plot(pred_lstm_inverse[:,1], pred_lstm_inverse[:,0], label='LSTM', color='orange', marker='x', markersize=2, linestyle='--')
plt.plot(pred_cnn_inverse[:,1], pred_cnn_inverse[:,0], label='CNN', color='blue', linestyle='--', alpha=0.7)
plt.plot(pred_kf_inverse[:,1], pred_kf_inverse[:,0], label='Kalman', color='green', linestyle='--', alpha=0.7)
plt.legend()
plt.title('Trajectory Prediction Comparison')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.tight_layout()
plt.savefig("trajectory_comparison_LSTM_CNN_KF.png", dpi=300)
plt.close()

