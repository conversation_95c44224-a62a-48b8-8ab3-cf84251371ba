import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from haversine import haversine
from filterpy.kalman import KalmanFilter

# ------------------ 1. 读取数据 ------------------
data = pd.read_csv('../../Geolife Trajectories 1.3\\Data\\001\\Trajectory\\20081024234405.plt', skiprows=6, header=None)
data.columns = ['lat', 'lon', 'zero', 'alt', 'date_days', 'date', 'time']
data = data[['lat', 'lon']]
print(f'样本数：{len(data)}, 维度：{data.shape[1]}')

# ------------------ 2. 归一化 ------------------
scaler = MinMaxScaler()
data_scaled = scaler.fit_transform(data)

# ------------------ 3. 构造滑动窗口 ------------------
def create_dataset(dataset, time_step=5):
    X, Y = [], []
    for i in range(len(dataset)-time_step):
        X.append(dataset[i:i+time_step, :])
        Y.append(dataset[i+time_step, :])
    return np.array(X), np.array(Y)

time_step = 5
X, Y = create_dataset(data_scaled, time_step)

# ------------------ 4. 划分训练集和测试集 ------------------
train_size = int(len(X)*0.8)
trainX, trainY = X[:train_size], Y[:train_size]
testX, testY = X[train_size:], Y[train_size:]
print('Train X shape:', trainX.shape)
print('Test X shape:', testX.shape)

# ------------------ 5. 卡尔曼滤波（完全数据泄露版本） ------------------
kf = KalmanFilter(dim_x=6, dim_z=2)
dt = 1.0
kf.F = np.array([
    [1,0,dt,0,0.5*dt**2,0],
    [0,1,0,dt,0,0.5*dt**2],
    [0,0,1,0,dt,0],
    [0,0,0,1,0,dt],
    [0,0,0,0,1,0],
    [0,0,0,0,0,1]
])
kf.H = np.array([[1,0,0,0,0,0],
                 [0,1,0,0,0,0]])
kf.P *= 0.01
kf.R = np.eye(2) * 0.001
kf.Q = np.eye(6) * 0.0001

# 初始化状态
kf.x[:2] = trainX[-1, -1, :].reshape(2,1)
v_lat = trainX[-1,-1,0] - trainX[-2,-1,0]
v_lon = trainX[-1,-1,1] - trainX[-2,-1,1]
a_lat = v_lat - (trainX[-2,-1,0] - trainX[-3,-1,0])
a_lon = v_lon - (trainX[-2,-1,1] - trainX[-3,-1,1])
kf.x[2:] = np.array([[v_lat], [v_lon], [a_lat], [a_lon]])

predicted = []

# ------------------ 6. 使用测试集真实点更新（数据泄露） ------------------
for i in range(len(testY)):
    kf.predict()
    kf.update(testY[i].reshape(2,1))  # 使用真实点更新
    predicted.append(kf.x[:2].flatten())

predicted = np.array(predicted)
predicted_inverse = scaler.inverse_transform(predicted)
testY_inverse = scaler.inverse_transform(testY)

# ------------------ 7. 误差计算 ------------------
errors_meter = []
for true_point, pred_point in zip(testY_inverse, predicted_inverse):
    dist = haversine((true_point[0], true_point[1]),
                     (pred_point[0], pred_point[1])) * 1000
    errors_meter.append(dist)

mean_error_meter = np.mean(errors_meter)
print(f"完全数据泄露卡尔曼预测平均误差: {mean_error_meter:.2f} 米")

# ------------------ 8. 可视化 ------------------
plt.figure(figsize=(8,6))
plt.plot(testY_inverse[:,1], testY_inverse[:,0], label='True trajectory', marker='o', markersize=2, linestyle='-')
plt.plot(predicted_inverse[:,1], predicted_inverse[:,0], label='Kalman Filter (Full Leak)', color='orange', marker='x', markersize=2, linestyle='--')
plt.legend()
plt.title('True vs Kalman Predicted Trajectory (Full Data Leakage)')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.tight_layout()
plt.savefig("trajectory_comparison_kalman_leak.png", dpi=300)
plt.show()
